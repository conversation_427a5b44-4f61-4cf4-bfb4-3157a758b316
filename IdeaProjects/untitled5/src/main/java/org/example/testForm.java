/*
 * Created by JFormDesigner on Fri Aug 01 14:00:56 EEST 2025
 */

package org.example;

import io.appium.java_client.android.AndroidDriver;
import io.github.ashwith.flutter.FlutterElement;
import io.github.ashwith.flutter.FlutterFinder;
import org.openqa.selenium.remote.DesiredCapabilities;
import io.appium.java_client.AppiumDriver;
//import io.appium.flutter.driver.FlutterFinder;
//import io.appium.flutter.driver.FlutterDriver;
import io.appium.java_client.android.AndroidDriver;
//import io.appium.java_client.remote.MobileCapabilityType;
import java.awt.*;
import java.awt.event.*;
import java.net.MalformedURLException;
import java.net.URL;
import javax.swing.*;

/**
 * <AUTHOR>
 */
public class testForm extends JFrame {
    private static AndroidDriver driver;
    private static FlutterFinder find;
    public testForm() {
        initComponents();

    }

    private void button1(ActionEvent e) {
        // TODO add your code here
        System.out.println("test test");
        DesiredCapabilities capabilities = getDesiredCapabilities();
        try {
            driver = new AndroidDriver(new URL("http://127.0.0.1:4723"), capabilities);
            //finder = new FlutterFinder(driver);
        } catch (MalformedURLException ex) {
            System.err.println("The URL for the Appium server is invalid.");
            ex.printStackTrace();
        }
    }

    private static DesiredCapabilities getDesiredCapabilities() {
        DesiredCapabilities capabilities = new DesiredCapabilities();
        capabilities.setCapability("platformName", "Android");
        capabilities.setCapability("appium:deviceName", "emulator-5554"); // Or your device's UDID
        capabilities.setCapability("appium:automationName", "Flutter"); // Flutter driver for Flutter apps
        capabilities.setCapability("appium:appPackage", "com.example.untitled1"); // IMPORTANT: Change this!
        capabilities.setCapability("appium:appActivity", ".MainActivity"); // Common default, may vary.

        // This capability is key: it tells Appium to attach to the app without resetting it.
        capabilities.setCapability("appium:noReset", true);
        return capabilities;
    }

    private void initComponents() {
        // JFormDesigner - Component initialization - DO NOT MODIFY  //GEN-BEGIN:initComponents  @formatter:off
        // Generated using JFormDesigner non-commercial license
        label1 = new JLabel();
        button1 = new JButton();

        //======== this ========
        setPreferredSize(new Dimension(630, 530));
        var contentPane = getContentPane();
        contentPane.setLayout(new FlowLayout());

        //---- label1 ----
        label1.setText("text");
        contentPane.add(label1);

        //---- button1 ----
        button1.setText("text");
        button1.addActionListener(e -> button1(e));
        contentPane.add(button1);
        pack();
        setLocationRelativeTo(getOwner());
        // JFormDesigner - End of component initialization  //GEN-END:initComponents  @formatter:on
    }

    // JFormDesigner - Variables declaration - DO NOT MODIFY  //GEN-BEGIN:variables  @formatter:off
    // Generated using JFormDesigner non-commercial license
    private JLabel label1;
    private JButton button1;
    // JFormDesigner - End of variables declaration  //GEN-END:variables  @formatter:on
}
