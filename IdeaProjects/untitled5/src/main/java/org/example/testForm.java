/*
 * Created by JFormDesigner on Fri Aug 01 14:00:56 EEST 2025
 */

package org.example;

import io.github.ashwith.flutter.FlutterElement;
import io.github.ashwith.flutter.FlutterFinder;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.remote.DesiredCapabilities;
import io.appium.java_client.AppiumDriver;

import java.awt.*;
import java.awt.event.*;
import java.net.MalformedURLException;
import java.net.URL;
import javax.swing.*;

/**
 * <AUTHOR>
 */
public class testForm extends JFrame {
    private static AppiumDriver driver;
    private static FlutterFinder find;
    public testForm() {
        initComponents();

    }

    private void button1(ActionEvent e) {
        // TODO add your code here
        System.out.println("test test");
        DesiredCapabilities capabilities = getDesiredCapabilities();
        try {
            driver = new AppiumDriver(new URL("http://127.0.0.1:4723/"), capabilities);
            System.out.println("Successfully connected to device!");
            System.out.println("App package source:");
            System.out.println(driver.getPageSource());
            WebElement element = driver.findElement(By.);
//            find = new FlutterFinder(driver);
//            FlutterElement kk= find.byText("Explore More");
//            kk.click();
        } catch (MalformedURLException ex) {
            throw new RuntimeException(ex);
        } catch (Exception ex) {
            System.err.println("Failed to connect: " + ex.getMessage());
            ex.printStackTrace();
        }
        // Note: FlutterFinder only works with Flutter automation driver
        // For UiAutomator2, use standard Appium element finding methods
        // Example: driver.findElement(By.id("element_id"));
    }

    private static DesiredCapabilities getDesiredCapabilities() {
        DesiredCapabilities capabilities = new DesiredCapabilities();
        capabilities.setCapability("platformName", "Android");
        capabilities.setCapability("appium:deviceName", "46211FDAS0082W"); // Or your device's UDID
        capabilities.setCapability("appium:automationName", "UiAutomator2"); // Use UiAutomator2 instead of Flutter
        capabilities.setCapability("appium:appPackage", "app.zod.com"); // IMPORTANT: Change this!
        capabilities.setCapability("appium:appActivity", ".MainActivity"); // Common default, may vary.

        // This capability is key: it tells Appium to attach to the app without resetting it.
        capabilities.setCapability("appium:noReset", true);
        return capabilities;
    }

    private void initComponents() {
        // JFormDesigner - Component initialization - DO NOT MODIFY  //GEN-BEGIN:initComponents  @formatter:off
        // Generated using JFormDesigner non-commercial license
        label1 = new JLabel();
        button1 = new JButton();

        //======== this ========
        setPreferredSize(new Dimension(630, 530));
        var contentPane = getContentPane();
        contentPane.setLayout(new FlowLayout());

        //---- label1 ----
        label1.setText("text");
        contentPane.add(label1);

        //---- button1 ----
        button1.setText("text");
        button1.addActionListener(e -> button1(e));
        contentPane.add(button1);
        pack();
        setLocationRelativeTo(getOwner());
        // JFormDesigner - End of component initialization  //GEN-END:initComponents  @formatter:on
    }

    // JFormDesigner - Variables declaration - DO NOT MODIFY  //GEN-BEGIN:variables  @formatter:off
    // Generated using JFormDesigner non-commercial license
    private JLabel label1;
    private JButton button1;
    // JFormDesigner - End of variables declaration  //GEN-END:variables  @formatter:on
}
