JFDML JFormDesigner: "8.2.4.0.393" Java: "21.0.7" encoding: "UTF-8"

new FormModel {
	contentType: "form/swing"
	root: new FormRoot {
		add( new FormWindow( "javax.swing.JFrame", new FormLayoutManager( class java.awt.FlowLayout ) ) {
			name: "this"
			"preferredSize": new java.awt.Dimension( 630, 530 )
			add( new FormComponent( "javax.swing.JLabel" ) {
				name: "label1"
				"text": "text"
			} )
			add( new FormComponent( "javax.swing.JButton" ) {
				name: "button1"
				"text": "text"
				addEvent( new FormEvent( "java.awt.event.ActionListener", "actionPerformed", "button1", true ) )
			} )
		}, new FormLayoutConstraints( null ) {
			"location": new java.awt.Point( 0, 0 )
			"size": new java.awt.Dimension( 740, 430 )
		} )
	}
}
